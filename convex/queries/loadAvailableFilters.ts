import {protectedQuery} from "@/convex/helpers";
import {v} from "convex/values";
import {
  LoadAvailableFiltersQueryHandler
} from "@/src/server/Gaming/application/queries/LoadAvailableFilters/LoadAvailableFiltersQueryHandler";
import {
  ConvexGameFilterListRepository
} from "@/src/server/Gaming/infrastructure/repositories/AvailableFilterList/ConvexGameFilterListRepository";
import {
  LoadAvailableFiltersWebPresenter
} from "@/src/server/Gaming/presentation/presenters/LoadAvailableFiltersWebPresenter";

export const endpoint = protectedQuery({
  args: {
    gameId: v.string(),
  },
  handler: async (ctx, {gameId}) => {
    const repository = new ConvexGameFilterListRepository(ctx);
    const queryHandler = new LoadAvailableFiltersQueryHandler(repository);
    const presenter = new LoadAvailableFiltersWebPresenter();

    await queryHandler.handle({gameId}, presenter);

    return presenter.getViewModel();
  },
});
