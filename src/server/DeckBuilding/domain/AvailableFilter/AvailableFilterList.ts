import {AvailableFilter} from "@/src/server/DeckBuilding/domain/AvailableFilter/AvailableFilter";

export class AvailableFilterList {
  public readonly availableFilters: AvailableFilter[];

  private constructor(availableFilters: AvailableFilter[]) {
    this.availableFilters = availableFilters;
  }

  static createFrom(availableFilters: AvailableFilter[]) {
    return new AvailableFilterList(availableFilters);
  }

  groupByDataProperty() {
    return this.availableFilters.reduce<Record<string, AvailableFilter[]>>((acc, filter) => {
      const key = filter.dataProperty;
      if (!acc[key]) acc[key] = [];
      acc[key].push(filter);
      return acc;
    }, {});
  }

}
