import {beforeEach, describe, expect, it} from 'vitest';
import {
  LoadAvailableFiltersQueryHandler
} from '@/src/server/Gaming/application/queries/LoadAvailableFilters/LoadAvailableFiltersQueryHandler';
import {
  InMemoryGameFilterListRepository
} from '@/src/server/Gaming/infrastructure/repositories/GameFilterList/InMemoryGameFilterListRepository';
import {GameFilter} from '@/src/server/Gaming/domain/GameFilter/GameFilter';
import {mock, MockProxy} from "vitest-mock-extended";
import {LoadAvailableFiltersPresenter} from "@/src/server/Gaming/application/ports/LoadAvailableFiltersPresenter";

describe('LoadAvailableFiltersQueryHandler', () => {
  let repository: InMemoryGameFilterListRepository;
  let presenter: MockProxy<LoadAvailableFiltersPresenter>;
  let handler: LoadAvailableFiltersQueryHandler;

  beforeEach(() => {
    repository = new InMemoryGameFilterListRepository();
    presenter = mock<LoadAvailableFiltersPresenter>();
    handler = new LoadAvailableFiltersQueryHandler(repository);
  });

  describe('When filters exist for the game', () => {
    it('should return them ordered by their order field', async () => {
      // Arrange
      const gameId = 'game-123';
      const filters: GameFilter[] = [
        {
          id: 'filter-2',
          text: 'Inkable',
          name: 'INKABLE',
          dataProperty: 'inkable',
          dataType: 'boolean',
          value: true,
          order: 2,
        },
        {
          id: 'filter-1',
          text: 'Amber',
          name: 'AMBER',
          dataProperty: 'color',
          dataType: 'string',
          value: 'AMBER',
          order: 1,
        },
      ];
      repository.addFiltersForGame(gameId, filters);

      // Act
      await handler.handle({gameId}, presenter);

      // Assert
      expect(presenter.display).toHaveBeenCalledWith({
        gameFilters: [
          {
            id: 'filter-1',
            text: 'Amber',
            name: 'AMBER',
            dataProperty: 'color',
            dataType: 'string',
            value: 'AMBER',
            order: 1,
          },
          {
            id: 'filter-2',
            text: 'Inkable',
            name: 'INKABLE',
            dataProperty: 'inkable',
            dataType: 'boolean',
            value: true,
            order: 2,
          },
        ],
      });
    });

    it('should group filters by data property', async () => {
      // Arrange
      const gameId = 'game-123';
      const filters: GameFilter[] = [
        {
          id: 'filter-1',
          text: 'Amber',
          name: 'AMBER',
          dataProperty: 'color',
          dataType: 'string',
          value: 'AMBER',
          order: 1,
        },
        {
          id: 'filter-2',
          text: 'Ruby',
          name: 'RUBY',
          dataProperty: 'color',
          dataType: 'string',
          value: 'RUBY',
          order: 2,
        },
        {
          id: 'filter-3',
          text: 'Inkable',
          name: 'INKABLE',
          dataProperty: 'inkable',
          dataType: 'boolean',
          value: true,
          order: 3,
        },
      ];
      repository.addFiltersForGame(gameId, filters);

      // Act
      await handler.handle({gameId}, presenter);

      // Assert
      expect(presenter.display).toHaveBeenCalledWith({
        gameFilters: [
          {
            id: 'filter-1',
            text: 'Amber',
            name: 'AMBER',
            dataProperty: 'color',
            dataType: 'string',
            value: 'AMBER',
            order: 1,
          },
          {
            id: 'filter-2',
            text: 'Ruby',
            name: 'RUBY',
            dataProperty: 'color',
            dataType: 'string',
            value: 'RUBY',
            order: 2,
          },
          {
            id: 'filter-3',
            text: 'Inkable',
            name: 'INKABLE',
            dataProperty: 'inkable',
            dataType: 'boolean',
            value: true,
            order: 3,
          },
        ],
      });
    });
  });

  describe('When no filters exist for the game', () => {
    it('should return an empty list', async () => {
      // Arrange
      const gameId = 'game-123';

      // Act
      await handler.handle({gameId}, presenter);

      // Assert
      expect(presenter.display).toHaveBeenCalledWith({
        gameFilters: [],
      });
    });
  });

  describe('When repository throws an error', () => {
    it('should display the error through presenter', async () => {
      // Arrange
      const gameId = 'game-123';
      const error = new Error('Repository error');
      vi.spyOn(repository, 'getByGameId').mockRejectedValue(error);

      // Act
      await handler.handle({gameId}, presenter);

      // Assert
      expect(presenter.displayError).toHaveBeenCalledWith(error.message);
    });
  });
});
