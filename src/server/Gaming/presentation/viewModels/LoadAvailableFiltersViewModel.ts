export type FilterViewModel = {
  id: string;
  name: string;
  text: string;
  dataProperty: string;
  dataType: 'string' | 'number' | 'boolean' | 'string[]';
  value: string | number | boolean | string[];
  order: number;
};

export type GroupedFiltersViewModel = Record<string, {
  name: string;
  filters: FilterViewModel[]
}>;

export type LoadAvailableFiltersViewModel = {
  error: string | null;
  data: {
    groupedFilters: GroupedFiltersViewModel;
    availableFilters: FilterViewModel[];
  } | null;
};