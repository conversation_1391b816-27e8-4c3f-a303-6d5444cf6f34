export type FilterViewModel =
  | { id: string, text: string; name: string; dataProperty: string; dataType: 'string'; value: string; order: number }
  | { id: string, text: string; name: string; dataProperty: string; dataType: 'number'; value: number; order: number }
  | { id: string, text: string; name: string; dataProperty: string; dataType: 'boolean'; value: boolean; order: number }
  | { id: string, text: string; name: string; dataProperty: string; dataType: 'string[]'; value: string; order: number };

export type GroupedFiltersViewModel = Record<string, {
  name: string;
  filters: FilterViewModel[]
}>;

export type LoadAvailableFiltersViewModel = {
  error: string | null;
  data: {
    groupedFilters: GroupedFiltersViewModel;
    availableFilters: FilterViewModel[];
  } | null;
};