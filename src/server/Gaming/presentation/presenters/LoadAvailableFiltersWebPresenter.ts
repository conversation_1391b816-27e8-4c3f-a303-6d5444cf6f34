import {GameFilterList} from "@/src/server/Gaming/domain/GameFilter/GameFilterList";
import {LoadGameFilterListPresenter} from "@/src/server/Gaming/application/ports/LoadGameFilterListPresenter";
import {LoadAvailableFiltersViewModel, FilterViewModel} from "@/src/server/Gaming/presentation/viewModels/LoadAvailableFiltersViewModel";
import {GameFilter} from "@/src/server/Gaming/domain/GameFilter/GameFilter";

export class LoadAvailableFiltersWebPresenter implements LoadGameFilterListPresenter {
  private viewModel: LoadAvailableFiltersViewModel = {
    error: null,
    data: null,
  };

  private mapGameFilterToViewModel(filter: GameFilter): FilterViewModel {
    const baseFilter = {
      id: filter.id,
      name: filter.name,
      text: filter.text,
      dataProperty: filter.dataProperty,
      order: filter.order,
    };

    switch (filter.dataType) {
      case 'string':
        return { ...baseFilter, dataType: 'string' as const, value: filter.value as string };
      case 'number':
        return { ...baseFilter, dataType: 'number' as const, value: filter.value as number };
      case 'boolean':
        return { ...baseFilter, dataType: 'boolean' as const, value: filter.value as boolean };
      case 'string[]':
        return { ...baseFilter, dataType: 'string[]' as const, value: filter.value as string };
      default:
        throw new Error(`Unknown dataType: ${filter.dataType}`);
    }
  }

  display(gameFilterList: GameFilterList): void {
    const groupedFilters = gameFilterList.groupByDataProperty();
    const mappedGroupedFilters = Object.entries(groupedFilters).reduce((acc, [key, group]) => {
      acc[key] = {
        name: group.name,
        filters: group.filters.map(filter => this.mapGameFilterToViewModel(filter)),
      };
      return acc;
    }, {} as Record<string, { name: string; filters: FilterViewModel[] }>);

    this.viewModel = {
      error: null,
      data: {
        groupedFilters: mappedGroupedFilters,
        availableFilters: gameFilterList.gameFilters.map(filter => this.mapGameFilterToViewModel(filter)),
      },
    };
  }

  displayError(error: string): void {
    this.viewModel = {
      error,
      data: null,
    };
  }

  getViewModel(): LoadAvailableFiltersViewModel {
    return this.viewModel;
  }
}
