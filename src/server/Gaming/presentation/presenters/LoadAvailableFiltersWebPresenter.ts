import {AvailableFilterList} from "@/src/server/DeckBuilding/domain/AvailableFilter/AvailableFilterList";
import {LoadAvailableFiltersPresenter} from "@/src/server/Gaming/application/ports/LoadAvailableFiltersPresenter";
import {LoadAvailableFiltersViewModel} from "@/src/server/Gaming/presentation/viewModels/LoadAvailableFiltersViewModel";

export class LoadAvailableFiltersWebPresenter implements LoadAvailableFiltersPresenter {
  private viewModel: LoadAvailableFiltersViewModel = {
    error: null,
    data: null,
  };

  display(availableFilterList: AvailableFilterList): void {
     this.viewModel = {
      error: null,
      data: {
        groupedFilters: availableFilterList.groupByDataProperty(),
        availableFilters: availableFilterList.availableFilters.map(filter => ({
          name: filter.name,
          text: filter.text,
          value: filter.value,
        })),
      },
    };
  }

  getViewModel(): LoadAvailableFiltersViewModel {
    return this.viewModel;
  }
}
