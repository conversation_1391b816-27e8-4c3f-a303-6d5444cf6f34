import {GameFilterList} from "@/src/server/Gaming/domain/GameFilter/GameFilterList";
import {LoadAvailableFiltersPresenter} from "@/src/server/Gaming/application/ports/LoadAvailableFiltersPresenter";
import {LoadAvailableFiltersViewModel, FilterViewModel} from "@/src/server/Gaming/presentation/viewModels/LoadAvailableFiltersViewModel";

export class LoadAvailableFiltersWebPresenter implements LoadAvailableFiltersPresenter {
  private viewModel: LoadAvailableFiltersViewModel = {
    error: null,
    data: null,
  };

  display(gameFilterList: GameFilterList): void {
    const groupedFilters = gameFilterList.groupByDataProperty();
    const mappedGroupedFilters = Object.entries(groupedFilters).reduce((acc, [key, group]) => {
      acc[key] = {
        name: group.name,
        filters: group.filters.map(filter => {
          switch (filter.dataType) {
            case 'string':
              return {
                id: filter.id,
                name: filter.name,
                text: filter.text,
                dataProperty: filter.dataProperty,
                dataType: 'string' as const,
                value: filter.value as string,
                order: filter.order,
              };
            case 'number':
              return {
                id: filter.id,
                name: filter.name,
                text: filter.text,
                dataProperty: filter.dataProperty,
                dataType: 'number' as const,
                value: filter.value as number,
                order: filter.order,
              };
            case 'boolean':
              return {
                id: filter.id,
                name: filter.name,
                text: filter.text,
                dataProperty: filter.dataProperty,
                dataType: 'boolean' as const,
                value: filter.value as boolean,
                order: filter.order,
              };
            case 'string[]':
              return {
                id: filter.id,
                name: filter.name,
                text: filter.text,
                dataProperty: filter.dataProperty,
                dataType: 'string[]' as const,
                value: filter.value as string,
                order: filter.order,
              };
            default:
              throw new Error(`Unknown dataType: ${filter.dataType}`);
          }
        }),
      };
      return acc;
    }, {} as Record<string, { name: string; filters: FilterViewModel[] }>);

    this.viewModel = {
      error: null,
      data: {
        groupedFilters: mappedGroupedFilters,
        availableFilters: gameFilterList.gameFilters.map(filter => {
          switch (filter.dataType) {
            case 'string':
              return {
                id: filter.id,
                name: filter.name,
                text: filter.text,
                dataProperty: filter.dataProperty,
                dataType: 'string' as const,
                value: filter.value as string,
                order: filter.order,
              };
            case 'number':
              return {
                id: filter.id,
                name: filter.name,
                text: filter.text,
                dataProperty: filter.dataProperty,
                dataType: 'number' as const,
                value: filter.value as number,
                order: filter.order,
              };
            case 'boolean':
              return {
                id: filter.id,
                name: filter.name,
                text: filter.text,
                dataProperty: filter.dataProperty,
                dataType: 'boolean' as const,
                value: filter.value as boolean,
                order: filter.order,
              };
            case 'string[]':
              return {
                id: filter.id,
                name: filter.name,
                text: filter.text,
                dataProperty: filter.dataProperty,
                dataType: 'string[]' as const,
                value: filter.value as string,
                order: filter.order,
              };
            default:
              throw new Error(`Unknown dataType: ${filter.dataType}`);
          }
        }),
      },
    };
  }

  displayError(error: string): void {
    this.viewModel = {
      error,
      data: null,
    };
  }

  getViewModel(): LoadAvailableFiltersViewModel {
    return this.viewModel;
  }
}
