import {GameFilter} from "@/src/server/Gaming/domain/GameFilter/GameFilter";

export class GameFilterList {
  public readonly gameFilters: GameFilter[];

  private constructor(gameFilters: GameFilter[]) {
    this.gameFilters = gameFilters;
  }

  static createFrom(gameFilters: GameFilter[]) {
    return new GameFilterList(gameFilters);
  }

  groupByDataProperty() {
    return this.gameFilters.reduce<Record<string, {
      name: string;
      filters: GameFilter[]
    }>>((acc, filter) => {
      const key = filter.dataProperty;
      if (!acc[key]) {
        acc[key] = {
          name: key,
          filters: []
        };
      }
      acc[key].filters.push(filter);
      return acc;
    }, {});
  }
}
