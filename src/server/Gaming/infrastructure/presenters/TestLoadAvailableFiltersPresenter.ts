import {LoadAvailableFiltersPresenter} from "@/src/server/Gaming/application/ports/LoadAvailableFiltersPresenter";
import {GameFilterList} from "@/src/server/Gaming/domain/GameFilter/GameFilterList";

export class TestLoadAvailableFiltersPresenter implements LoadAvailableFiltersPresenter {
  private result: GameFilterList | null = null;
  private errorMessage: string | null = null;

  display(gameFilterList: GameFilterList): void {
    this.result = gameFilterList;
    this.errorMessage = null;
  }

  displayError(error: string): void {
    this.errorMessage = error;
    this.result = null;
  }

  getResult(): GameFilterList | null {
    return this.result;
  }

  getError(): string | null {
    return this.errorMessage;
  }

  hasResult(): boolean {
    return this.result !== null;
  }

  hasError(): boolean {
    return this.errorMessage !== null;
  }
}
