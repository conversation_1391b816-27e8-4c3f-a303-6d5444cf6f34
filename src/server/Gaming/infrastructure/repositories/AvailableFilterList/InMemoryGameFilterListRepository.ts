import {AvailableFilterListRepository} from "@/src/server/Gaming/application/ports/AvailableFilterListRepository";
import {GameFilterList} from "@/src/server/Gaming/domain/GameFilter/GameFilterList";
import {GameFilter} from "@/src/server/Gaming/domain/GameFilter/GameFilter";

export class InMemoryGameFilterListRepository implements AvailableFilterListRepository {
  private filters: Map<string, GameFilter[]> = new Map();

  async getByGameId(gameId: string): Promise<GameFilterList> {
    const gameFilters = this.filters.get(gameId) || [];
    const sortedFilters = gameFilters.sort((a, b) => a.order - b.order);
    return GameFilterList.createFrom(sortedFilters);
  }

  addFiltersForGame(gameId: string, filters: GameFilter[]): void {
    this.filters.set(gameId, filters);
  }

  clear(): void {
    this.filters.clear();
  }
}
