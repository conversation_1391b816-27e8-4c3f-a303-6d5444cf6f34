import {GenericQueryCtx} from "convex/server";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import {getManyFrom} from "convex-helpers/server/relationships";
import {GameFilterList} from "@/src/server/Gaming/domain/GameFilter/GameFilterList";
import {AvailableFilterListRepository} from "@/src/server/Gaming/application/ports/AvailableFilterListRepository";

export class ConvexAvailableFilterListRepository implements AvailableFilterListRepository {
  private readonly ctx: GenericQueryCtx<DataModel>;

  constructor(ctx: GenericQueryCtx<DataModel>) {
    this.ctx = ctx;
  }

  async getByGameId(gameId: string): Promise<GameFilterList> {
    const availableFilters = await getManyFrom(this.ctx.db, "availableFilters", "by_gameId", gameId as Id<"games">);
    const sortedFilters = availableFilters
      .map(doc => ({
        id: doc._id,
        text: doc.text,
        name: doc.name,
        dataProperty: doc.dataProperty,
        dataType: doc.dataType as 'boolean' | 'string' | 'number' | 'string[]',
        value: doc.value,
        order: doc.order,
      }))
      .sort((a, b) => a.order - b.order);

    return GameFilterList.createFrom(sortedFilters);
  }
}
