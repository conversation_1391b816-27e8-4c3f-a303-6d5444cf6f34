import {
  LoadAvailableFiltersQuery
} from "@/src/server/Gaming/application/queries/LoadAvailableFilters/LoadAvailableFiltersQuery";
import {LoadAvailableFiltersPresenter} from "@/src/server/Gaming/application/ports/LoadAvailableFiltersPresenter";
import {AvailableFilterListRepository} from "@/src/server/Gaming/application/ports/AvailableFilterListRepository";

export class LoadAvailableFiltersQueryHandler {
  private readonly repository: AvailableFilterListRepository;

  constructor(repository: AvailableFilterListRepository) {
    this.repository = repository;
  }

  async handle({gameId}: LoadAvailableFiltersQuery, presenter: LoadAvailableFiltersPresenter) {
    const gameFilterList = await this.repository.getByGameId(gameId);
    presenter.display(gameFilterList);
  }
}
