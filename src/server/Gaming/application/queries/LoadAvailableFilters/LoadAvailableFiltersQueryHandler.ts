import {
  LoadAvailableFiltersQuery
} from "@/src/server/Gaming/application/queries/LoadAvailableFilters/LoadAvailableFiltersQuery";
import {LoadAvailableFiltersPresenter} from "@/src/server/Gaming/application/ports/LoadAvailableFiltersPresenter";
import {GameFilterListRepository} from "@/src/server/Gaming/application/ports/GameFilterListRepository";

export class LoadAvailableFiltersQueryHandler {
  private readonly repository: GameFilterListRepository;

  constructor(repository: GameFilterListRepository) {
    this.repository = repository;
  }

  async handle({gameId}: LoadAvailableFiltersQuery, presenter: LoadAvailableFiltersPresenter) {
    try {
      const gameFilterList = await this.repository.getByGameId(gameId);
      presenter.display(gameFilterList);
    } catch (error) {
      presenter.displayError(error instanceof Error ? error.message : "Unknown error occurred");
    }
  }
}
