'use client';

import {FC, memo, useEffect} from 'react';
import {CheckboxCards, Flex, Heading, ScrollArea, Text} from '@radix-ui/themes';
import {useDeckBuilder} from '@/src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder';
import {FilterItem} from '@/src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/FilterItem';
import {Filter} from '@/src/client/DeckBuilding/domain/Catalog/Filter';
import {GroupedFiltersViewModel} from "@/src/server/Gaming/presentation/viewModels/LoadAvailableFiltersViewModel";

type Props = {
  groupedFilters: GroupedFiltersViewModel,
  availableFilters: Filter[];
};

const DeckBuildingFilters: FC<Props> = ({groupedFilters, availableFilters}) => {
  const {filterCatalog, activeFilters, loadAvailableFilters} = useDeckBuilder();

  useEffect(() => {
    loadAvailableFilters(availableFilters);
  }, [loadAvailableFilters, availableFilters]);

  return (
    <Flex direction="column" className="h-full" gap="3">
      <Flex justify="between" align="center">
        <Flex direction="column" pl="1">
          <Heading size="4">Filters</Heading>
          <Text as="span" size="2" color="gray">
            {activeFilters.length}/{availableFilters.length} active
          </Text>
        </Flex>
      </Flex>

      <ScrollArea type="always" scrollbars="vertical" className="h-full">
        <Flex direction="column" justify="end" gap="4" pr="4">
          {Object.entries(groupedFilters).map(([groupName, filters]) => (
            <Flex key={groupName} direction="column" gap="2">
              <Text size="2" weight="bold" color="iris">
                {groupName}
              </Text>
              <CheckboxCards.Root
                color="iris"
                size="1"
                gap="1"
                value={activeFilters}
                onValueChange={filterCatalog}
                columns={{initial: '1'}}
              >
                {filters.map(filter => (
                  <FilterItem key={filter.name} filter={filter}/>
                ))}
              </CheckboxCards.Root>
            </Flex>
          ))}
        </Flex>
      </ScrollArea>
    </Flex>
  );
};

export default memo(DeckBuildingFilters);
